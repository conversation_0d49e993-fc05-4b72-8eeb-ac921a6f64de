<template>
  <DocumentEditor
    v-if="state.config"
    :id="state.file?.fileId"
    documentServerUrl="http://************:8060"
    :config="state.config" />
</template>

<script setup lang="ts">
  import { reactive } from 'vue';
  import { DocumentEditor } from '@onlyoffice/document-editor-vue';
  import { onlyOfficeConfig } from '@/api/basic/common'

  defineExpose({ init, close });

  // 将原来的const ref变量封装到state中
  const state = reactive({
    config: null,
    file: null,
  });

  async function init(file) {
    state.file = file;
    onlyOfficeConfig({
      fileId: file.fileId,
      fileName: file.name,
    }).then(res => {
      state.config = res.data;
    });
  }

  function close() {
    state.config = null;
    state.file = null;
  }
</script>

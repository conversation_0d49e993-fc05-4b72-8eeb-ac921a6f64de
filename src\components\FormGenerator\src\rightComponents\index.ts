export { default as RInput } from './RInput.vue';
export { default as RTextarea } from './RTextarea.vue';
export { default as RInputNumber } from './RInputNumber.vue';
export { default as RSwitch } from './RSwitch.vue';
export { default as RRadio } from './RRadio.vue';
export { default as RCheckbox } from './RCheckbox.vue';
export { default as RSelect } from './RSelect.vue';
export { default as RCascader } from './RCascader.vue';
export { default as RTreeSelect } from './RTreeSelect.vue';
export { default as RDatePicker } from './RDatePicker.vue';
export { default as RTimePicker } from './RTimePicker.vue';
export { default as RUploadFile } from './RUploadFile.vue';
export { default as RUploadImg } from './RUploadImg.vue';
export { default as RColorPicker } from './RColorPicker.vue';
export { default as RIconPicker } from './RIconPicker.vue';
export { default as REmbedForm } from './REmbedForm.vue';
export { default as RRate } from './RRate.vue';
export { default as RSlider } from './RSlider.vue';
export { default as REditor } from './REditor.vue';
export { default as RLink } from './RLink.vue';
export { default as RButton } from './RButton.vue';
export { default as RText } from './RText.vue';
export { default as RAlert } from './RAlert.vue';
export { default as RQrcode } from './RQrcode.vue';
export { default as RBarcode } from './RBarcode.vue';
export { default as ROrgRight } from './ROrgRight.vue';
export { default as RTable } from './RTable/index.vue';
export { default as RPopupSelect } from './RPopupSelect.vue';
export { default as RAreaSelect } from './RAreaSelect.vue';
export { default as RRelationForm } from './RRelationForm/index.vue';
export { default as RRelationFormAttr } from './RRelationFormAttr.vue';
export { default as RPopupAttr } from './RPopupAttr.vue';
export { default as RCalculate } from './RCalculate.vue';
export { default as RGroupTitle } from './RGroupTitle.vue';
export { default as RDivider } from './RDivider.vue';
export { default as RCollapse } from './RCollapse.vue';
export { default as RTab } from './RTab.vue';
export { default as RRow } from './RRow.vue';
export { default as RCard } from './RCard.vue';
export { default as RTableGrid } from './RTableGrid.vue';
export { default as RTableGridTd } from './RTableGridTd.vue';
export { default as RAutoComplete } from './RAutoComplete.vue';
export { default as RLocation } from './RLocation/index.vue';
export { default as RSign } from './RSign.vue';
export { default as RSignature } from './RSignature.vue';
export { default as RIframe } from './RIframe.vue';
export { default as RSteps } from './RSteps.vue';
export { default as RBillRule } from './RBillRule.vue';
export { default as RDataLogList } from './RDataLogList.vue';

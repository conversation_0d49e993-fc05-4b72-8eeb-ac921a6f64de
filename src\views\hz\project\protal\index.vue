<template>
  <div class="top">
    <div class="top-one" v-if="details">
      <div class="block">
        <img src="@/assets/images/benefit1.png" alt="" />
        <div class="block-right">
          <div style="font-weight: 900">项目经理</div>
          <div style="color: #7d23ff">{{ details.project_manager }}</div>
          <div style="color: #c4c4c4">{{ details.project_manager_position }}</div>
        </div>
      </div>
      <div class="block">
        <img src="@/assets/images/benefit2.png" alt="" />
        <div class="block-right">
          <div style="font-weight: 900">执行经理</div>
          <div style="color: #fb9500">{{ details.execution_manager }}</div>
        </div>
      </div>
    </div>
    <div class="top-two">
      <div class="titleE">本项目任务进度</div>
      <div class="titleC">Task Progress</div>
        <div class="top-two-bottom">
          <a-popover
            :title="item.f_custom_task_name"
            placement="right"
            v-for="item in grouped"
            class="top-two-bottom-item"
          >
            <template #content>
              <p>任务进度：{{ item.f_progress + "%" }}</p>
            </template>
            <div>
              <div class="top-two-bottom-item-di">
                <div
                  class="top-two-bottom-item-inner"
                  :style="{ height: item.f_progress + '%' }"
                ></div>
              </div>
            </div>
          </a-popover>
          <!-- <progressChart :grouped="grouped" /> -->
        </div>
    </div>
    <div class="top-three">
      <div class="titleE">项目合同收款趋势</div>
      <div class="titleC">Task Progress</div>
      <contractChart />
    </div>
    <div class="top-four">
      <div class="titleE">任务进度</div>
      <div class="titleC">Task Progress</div>
      <div
        style="
          width: 100%;
          display: flex;
          align-items: center;
          margin-top: 10px;
          flex-direction: column;
        "
      >
        <a-progress
          type="dashboard"
          :percent="Math.round((count / allCount) * 100)"
          :size="122"
          :strokeWidth="10"
          :gapDegree="65"
        />
        <div
          class="title-item"
          style="background-color: #e7f0ff; color: #1b9eff; margin-top: 20px"
        >
          <div>待处理</div>
          <div style="font-size: 20px; color: #000">{{ allCount - count }}</div>
          <ArrowRightOutlined :style="{ color: '#1B9EFF' }" />
        </div>
        <div class="title-item" style="background-color: #ffe7e7; color: #ff4e4e; margin-top: 10px">
          <div>已完成</div>
          <div style="font-size: 20px; color: #000">{{ count }}</div>
          <ArrowRightOutlined :style="{ color: '#FF4E4E' }" />
        </div>
      </div>
    </div>
  </div>
  <div class="bottom">
    <div class="bottom-one">
      <a-table
        :columns="columns"
        :data-source="grouped"
        class="bottom-one-inner"
        size="small"
        :pagination="false"
        :scroll="{ y: 200 }"
      >
        <template #bodyCell="{ column, text }">
          <template v-if="column.dataIndex === 'name'">
            <a>{{ text }}</a>
          </template>
          <template v-else-if="column.dataIndex === 'f_priority'">
            <span>
              <a-tag :color="text === '普通' ? 'purple' : text === '重要' ? 'orange' : 'red'">
                {{ text }}
              </a-tag>
            </span>
          </template>
        </template>

        <template #title>
          <div class="titleE">项目任务执行情况</div>
          <div class="titleC">Implementation</div>
        </template>
      </a-table>
    </div>
    <div class="bottom-two">
      <div class="titleE">开票趋势</div>
      <div style="color: #cc1717; font-weight: 900; font-size: 22px; margin-top: 10px">￥23122</div>
      <invoiceChart />
    </div>
    <div class="bottom-three">
      <div class="titleE">项目基本信息</div>
      <div class="bottom-three-item">
        <div class="bottom-three-item-top">
          <PayCircleOutlined :style="{ color: '#178FFF' }" />
          <div style="margin-left: 10px">合同金额</div>
        </div>
        <div class="bottom-three-item-border" v-if="details">{{ details.f_contract_amount }}元</div>
      </div>
      <div class="bottom-three-item">
        <div class="bottom-three-item-top">
          <PayCircleOutlined :style="{ color: '#178FFF' }" />
          <div style="margin-left: 10px">业务期限</div>
        </div>
        <div class="bottom-three-item-border" v-if="details">
          {{ formatToDate(details.f_contract_start_time) }}-{{
            formatToDate(details.f_contract_end_time)
          }}
        </div>
      </div>
    </div>
  </div>
</template>
<script lang="ts" setup>
import { formatToDate } from "@/utils/dateUtil";
import { ref, onMounted, reactive } from "vue";
import { Modal as AModal } from "ant-design-vue";
import { onlineUtils, getAmountChinese } from "@/utils/jnpf";
import { updateModel, getModelInfo, getModelList } from "@/api/onlineDev/visualDev";
import { getUserInfo } from "@/api/permission/user";
import contractChart from "./components/contract.vue";
import invoiceChart from "./components/invoice.vue";
import progressChart from "./components/progress.vue";

import { ArrowRightOutlined, PayCircleOutlined, CalendarOutlined } from "@ant-design/icons-vue";
const details = ref<any>(null);
const grouped = ref<any>();
const allCount = ref<any>(0);
const count = ref<any>(0);
const columns = [
  {
    title: "任务成员",
    dataIndex: "f_task_user_id",
  },
  {
    title: "任务名称",
    dataIndex: "f_custom_task_name",
  },
  {
    title: "风险程度",
    dataIndex: "f_priority",
  },
  {
    title: "任务进度%",
    dataIndex: "f_progress",
  },
];

async function init() {
  getModelInfo("708293041642802053", localStorage.getItem("project_id")).then((res) => {
    details.value = JSON.parse(res.data.data);
    onlineUtils
      .request("/api/system/DataInterface/726047730006234245/Actions/Preview?tenantId=0", "post", {
        paramList: [
          {
            id: "8453b0",
            field: "project_code",
            defaultValue: localStorage.getItem("project_code"),
            fieldName: "",
            dataType: "varchar",
            required: 0,
          },
        ],
      })
      .then((resData) => {
        resData.data.forEach((element) => {
          if (element.manager_type == "project_manager") {
            details.value.project_manager = element.f_real_name;
            details.value.project_manager_position = element.position_name;
          }
          if (element.manager_type == "execution_manager") {
            details.value.execution_manager = element.f_real_name;
            details.value.execution_manager_position = element.position_name;
          }
        });
      });
  });

  try {
    let reData = await getModelList({
      modelId: "707984386888305541",
      pageSize: 1000000,
      extraQueryJson: JSON.stringify({ f_project_code: localStorage.getItem("project_code") }),
    });
    grouped.value = reData?.data?.list;
    allCount.value = grouped.value.length;
    count.value = grouped.value.filter((item) => item.f_task_status === "已完成").length;
  } catch (error) {
    console.error("获取任务列表失败:", error);
  }
}
onMounted(() => {
  init();
});
</script>

<style lang="less" scoped>
.index {
  min-width: 1500px;
}
.titleE {
  font-weight: 900;
  font-size: 17px;
}
.titleC {
  color: #aeaeae;
  font-size: 13px;
}
.top {
  padding: 1.5vw 30px;
  box-sizing: border-box;
  display: flex;
  justify-content: space-between;
  width: 100%;
  gap: 1.5vw;
  &-one {
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    width: 18%;
    gap: 1.5vw;
    .block {
      width: 100%;
      height: 47%;
      border-radius: 5px;
      background-color: #fff;
      display: flex;
      justify-content: center;
      align-items: center;
      padding: 15px 2%;
      box-sizing: border-box;
      box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05), 0 4px 12px rgba(0, 0, 0, 0.08);
      line-height: 23px;
      font-size: 16px;
      gap: 5%;
      img {
        padding-right: 10px;
        width: 40%;
      }
    }
  }

  &-two {
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05), 0 4px 12px rgba(0, 0, 0, 0.08);
    width: 30%;
    padding: 20px 30px;
    box-sizing: border-box;
    border-radius: 5px;
    &-bottom {
      margin-top: 50px;
      white-space: nowrap;
      overflow-x: auto;
      &-item {
        height: 100%;
        width: 7%;
        margin-right: 20px;
        flex-shrink: 0;
        cursor: pointer;
        display: inline-block;
        &-di {
          width: 100%;
          height: 170px;
          background-color: #e9ecf1;
          border-radius: 5px;
          position: relative;
        }
        &-inner {
          width: 100%;
          position: absolute;
          background-color: #1a9dff;
          border-radius: 5px;
          bottom: 0;
          left: 0;
          right: 0;
        }
      }
    }
    // &-bottom::-webkit-scrollbar {
    //   display: none; /* Chrome, Safari, Opera */
    // }
  }

  &-three {
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05), 0 4px 12px rgba(0, 0, 0, 0.08);
    width: 30%;
    padding: 20px 30px;
    box-sizing: border-box;
    border-radius: 5px;
  }
  &-four {
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05), 0 4px 12px rgba(0, 0, 0, 0.08);
    width: 17%;
    padding: 20px 30px;
    box-sizing: border-box;
    border-radius: 5px;
    .title-item {
      display: flex;
      width: 80%;
      line-height: 35px;
      border-radius: 10px;
      justify-content: space-around;
    }
  }
}
.bottom {
  padding:0 30px 30px 30px;
  box-sizing: border-box;
  display: flex;
  justify-content: space-between;
  gap: 1.5vw;
  &-one {
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05), 0 4px 12px rgba(0, 0, 0, 0.08);
    width: 40%;
    padding: 20px 30px;
    box-sizing: border-box;
    border-radius: 5px;
    &-inner {
      width: 100%;
    }
  }
  &-two {
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05), 0 4px 12px rgba(0, 0, 0, 0.08);
    width: 30%;
    padding: 20px 30px;
    box-sizing: border-box;
    border-radius: 5px;
  }
  &-three {
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05), 0 4px 12px rgba(0, 0, 0, 0.08);
    width: 30%;
    padding: 20px 30px;
    box-sizing: border-box;
    border-radius: 5px;
    &-item {
      margin-top: 30px;
      &-top {
        display: flex;
        margin-bottom: 20px;
        font-size: 22px;
      }
      &-border {
        margin-left: 30px;
        border: 1px solid #a3aed0;
        height: 42px;
        line-height: 40px;
        border-radius: 4px;
        display: inline-block;
        padding: 0 20px;
        font-size: 20px;
      }
    }
  }
}

@media (max-width: 1500px) {
  .top {
    width: 1500px;
  }
  .bottom {
    width: 1500px;
  }
}
</style>

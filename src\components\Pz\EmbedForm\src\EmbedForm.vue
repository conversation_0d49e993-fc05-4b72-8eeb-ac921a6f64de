<template>
  <div class="embed-form-container" v-if="menuId && formId && config">
    <List class="sublist" :key="menuId" :config="config" :modelId="formId" :menuId="menuId" :noSearch="isSimple" :noToolbar="isSimple" />
  </div>
  <div class="embed-form-placeholder" v-else>
    <jnpf-empty description="未完成配置" />
  </div>
</template>
<script lang="ts" setup>
import { ref, onMounted, watch, inject } from "vue";
  import { getConfigData } from '@/api/onlineDev/visualDev';
  import { createAsyncComponent } from '@/utils/factory/createAsyncComponent';
  const List = createAsyncComponent(() => import('@/views/common/dynamicModel/list/index.vue'))

  defineOptions({ name: 'JnpfEmbedForm', inheritAttrs: false });

  const props = defineProps({
    menuId: { type: String, default: '' },
    formId: { type: String, default: '' },
    isFlow: { type: Boolean, default: false },
    isSimple: { type: Boolean, default: true },
  });

  const config :any = ref(null);
  const parameter :any = inject('parameter');

  watch(
    () => [props.isFlow],
    () => {
      init();
    },
  );

  function init() {
    if (!props.menuId || !props.formId) return;
    getConfigData(props.formId).then(res => {
      config.value = res.data;
      if (!config.value) return;
      config.value.id = config.value.id || props.formId;
      // TODO 解耦 f_task_code
      config.value.extraQueryJson = JSON.stringify({ f_task_code: parameter?.formData?.f_task_code });
      config.value.enableFlow = props.isFlow;
    });
  }

  onMounted(() => {
    init();
  });
</script>
<style lang="less" scoped>
  @prefix-cls: ~'@{namespace}-embed-form';

  .@{prefix-cls} {
    width: 100%;

    .embed-form-container {
      width: 100%;
      height: 100%;

      .embed-form-iframe {
        width: 100%;
        height: var(--height);
        border: var(--borderWidth) var(--borderType) var(--borderColor);
        border-radius: 4px;
      }
    }

    .embed-form-placeholder {
      width: 100%;
      height: 200px;
      display: flex;
      align-items: center;
      justify-content: center;
      border: 1px dashed #d9d9d9;
      border-radius: 4px;
      background-color: #fafafa;
    }
  }
</style>

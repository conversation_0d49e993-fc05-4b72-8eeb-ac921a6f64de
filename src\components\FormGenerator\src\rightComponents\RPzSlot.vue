<template>
  <a-form-item label="插槽名称">
    <a-input
      v-model:value="activeData.slotName"
      placeholder="请输入插槽名称"
      :disabled="activeData.disabled" />
  </a-form-item>
  <a-form-item label="插槽数据">
    <a-textarea
      v-model:value="slotDataStr"
      placeholder="请输入插槽数据(JSON格式)"
      :rows="4"
      :disabled="activeData.disabled"
      @blur="handleSlotDataChange" />
  </a-form-item>
  <a-form-item label="说明">
    <div class="slot-help-text">
      <p>插槽名称：定义插槽的唯一标识符</p>
      <p>插槽数据：传递给插槽的数据对象(JSON格式)</p>
      <p>使用方式：在引入表单时通过具名插槽注入自定义组件</p>
    </div>
  </a-form-item>
</template>

<script lang="ts" setup>
  import { ref, watch, computed } from 'vue';
  
  defineOptions({ inheritAttrs: false });
  const props = defineProps(['activeData']);
  
  const renderKey = ref(+new Date());
  
  // 将 slotData 对象转换为字符串用于编辑
  const slotDataStr = ref('');
  
  // 初始化 slotDataStr
  watch(() => props.activeData.slotData, (newVal) => {
    if (newVal && typeof newVal === 'object') {
      slotDataStr.value = JSON.stringify(newVal, null, 2);
    } else {
      slotDataStr.value = '';
    }
  }, { immediate: true });
  
  // 处理插槽数据变化
  function handleSlotDataChange() {
    try {
      if (slotDataStr.value.trim()) {
        props.activeData.slotData = JSON.parse(slotDataStr.value);
      } else {
        props.activeData.slotData = {};
      }
    } catch (error) {
      console.warn('插槽数据格式错误，请输入有效的JSON格式');
      // 重置为空对象
      props.activeData.slotData = {};
      slotDataStr.value = '';
    }
  }
</script>

<style lang="less" scoped>
.slot-help-text {
  font-size: 12px;
  color: #666;
  line-height: 1.5;
  
  p {
    margin: 4px 0;
  }
}
</style>

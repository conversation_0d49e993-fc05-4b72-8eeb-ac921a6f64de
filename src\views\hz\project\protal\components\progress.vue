<template>
  <div ref="chartRef" style="width: 100%; height: 220px"></div>
</template>
<script lang="ts" setup>
import { useEChart } from "@/components/VisualPortal/Design/hooks/useEChart";
import { onMounted, ref, Ref, watch } from "vue";

const props = defineProps(["grouped"]);

let activeData = {
  jnpfKey: "barChart",
  dataType: "static",
  propsApi: "",
  option: {
    styleType: 4,
    defaultValue: [],
    titleText: "",
    titleTextStyleColor: "#303133",
    titleTextStyleFontSize: 18,
    titleTextStyleFontWeight: false,
    titleLeft: "center",
    titleBgColor: "",
    titleSubtext: "",
    titleSubtextStyleColor: "#303133",
    titleSubtextStyleFontSize: 12,
    titleSubtextStyleFontWeight: false,
    seriesBarWidth: 30,
    seriesItemStyleBarBorderRadius: 5,
    xAxisName: "",
    xAxisNameTextStyleFontSize: 14,
    xAxisNameTextStyleColor: "#303133",
    xAxisNameTextStyleFontWeight: false,
    xAxisShow: false,
    category: "category",
    xAxisAxisLineLineStyleColor: "#303133",
    xAxisAxisLabelRotate: 0,
    xAxisAxisLabelTextStyleFontSize: 14,
    xAxisAxisLabelTextStyleColor: "#303133",
    xAxisAxisLabelTextFontWeight: false,
    xAxisSplitLineShow: false,
    xAxisSplitLineLineStyleColor: "#DFDFDF",
    xAxisInverse: false,
    yAxisName: "",
    yAxisNameTextStyleFontSize: 14,
    yAxisNameTextStyleColor: "#303133",
    yAxisNameTextStyleFontWeight: false,
    yAxisShow: false,
    yAxisAxisLineLineStyleColor: "#303133",
    yAxisSplitLineShow: true,
    yAxisSplitLineLineStyleColor: "#DFDFDF",
    yAxisAxisLabelTextStyleFontSize: 14,
    yAxisAxisLabelTextStyleColor: "#303133",
    yAxisAxisLabelTextFontWeight: false,
    seriesLabelShow: false,
    seriesLabelFontSize: 14,
    seriesLabelFontWeight: false,
    seriesLabelColor: "#303133",
    seriesLabelBgColor: "",
    tooltipShow: true,
    tooltipTextStyleFontSize: 14,
    tooltipTextStyleFontWeight: false,
    tooltipTextStyleColor: "#303133",
    tooltipBgColor: "#fff",
    gridLeft: 0,
    gridTop: 15,
    gridRight: 72,
    gridBottom: 20,
    legendShow: false,
    legendTextStyleFontSize: 14,
    legendOrient: "horizontal",
    legendLeft: 0,
    legendTop: 0,
    AxisTextStyleColor: "",
    AxisLineStyleColor: "",
    colorList: [
      {
        color1: "#64A4F2",
        color2: "#FFFFFF",
      },
    ],
    barType: [],
    target: "_self",
    urlAddress: "",
  },
  refresh: {
    autoRefresh: false,
    autoRefreshTime: 5,
  },
};
const chartRef = ref<HTMLDivElement | null>(null);
const { CardHeader, init } = useEChart(activeData, chartRef as Ref<HTMLDivElement>);

watch(
  async () => props.grouped,
  (newVal, oldVal) => {
    if (newVal) {
      let current = JSON.parse(JSON.stringify(props.grouped));
      activeData.option.defaultValue = current.map((item) => {
        return {
          name: item.f_custom_task_name,
          type: "任务进度",
          value: item.f_progress,
        };
      });
      init();
    }
  }
);

</script>

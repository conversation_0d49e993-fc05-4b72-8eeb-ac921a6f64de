<template>
  <a-modal
    v-model:open="visible"
    :footer="null"
    :closable="false"
    :keyboard="false"
    :maskClosable="false"
    class="common-container-modal jnpf-full-modal full-modal file-preview-modal"
    wrap-class-name="fullscreen-modal">
    <template #title>
      <div class="jnpf-full-modal-header">
        <div class="header-title">
          <p class="header-txt">在线表单 - 动态投资分析表</p>
        </div>
        <a-space class="options" :size="10">
          <a-button type="primary" @click="">生成Excel</a-button>
          <a-button @click="handleCancel()">关闭</a-button>
        </a-space>
      </div>
    </template>
    <div class="basic-content bg-white" v-loading="loading">
      <List v-if="config" ref="listRef" :config="config" :modelId="modelId" :menuId="menuId">
      </List>
    </div>
  </a-modal>
</template>
<script lang="ts" setup>
import { reactive, toRefs } from 'vue';
import { Modal as AModal } from 'ant-design-vue';
import List from "@/views/common/dynamicModel/list/index.vue";
import { getConfigData } from "@/api/onlineDev/visualDev";
import { useMessage } from "@/hooks/web/useMessage";
const { createMessage } = useMessage();

interface State {
  visible: boolean;
  loading: boolean;
  title: string;
  url: string;
  file: any;
  isEditing: boolean;
  config: any;
  modelId: string;
  menuId: string;
}

defineEmits(['register']);
const state = reactive<State>({
  visible: false,
  loading: false,
  title: '',
  url: '',
  file: {},
  isEditing: false,
  config: null,
  modelId: '',
  menuId: '',
});

const { visible, loading, config, modelId, menuId } = toRefs(state);

defineExpose({ init });

function init(fruitData: any) {
  console.log('初始化动态投资分析表', fruitData);

  state.visible = true;
  state.loading = true;
  state.modelId = '728628919510506501';
  state.menuId = '728632626381855749';
  getConfigData(state.modelId).then(res => {
    if (res.code !== 200 || !res.data) {
      createMessage.error(res.msg || '请求出错');
      return;
    }
    state.config = res.data;
    state.config.id = state.config.id || state.modelId;

    localStorage.setItem('autoFillFormData', JSON.stringify({
      f_project_code: fruitData.f_project_code,
      f_task_code: fruitData.f_task_code,
      f_fruit_code: fruitData.f_fruit_code,
    }))
  }).finally(()=> {
    state.loading = false;
  });
}

async function handleCancel() {
  state.visible = false;
  state.loading = false;
  localStorage.removeItem('autoFillFormData');
}

</script>

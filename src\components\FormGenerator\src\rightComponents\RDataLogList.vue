<template>
  <a-form-item label="说明">
    <div class="text-gray-500 text-sm">
      <p>请确保在表单属性中开启了“修改记录”</p>
    </div>
  </a-form-item>
  <a-form-item>
    <template #label>
      模型ID
      <BasicHelp text="默认为当前表单，通常无需修改" />
    </template>
    <a-input v-model:value="activeData.modelId" placeholder="请输入模型ID" />
  </a-form-item>
</template>

<script lang="ts" setup>
  import { BasicHelp } from '@/components/Basic';
  import { onMounted } from 'vue';

  const props = defineProps(['activeData', 'formInfo']);

  onMounted(() => {
    if (!props.activeData.modelId && props.formInfo.id) {
      props.activeData.modelId = props.formInfo.id;
    }
  });
</script>

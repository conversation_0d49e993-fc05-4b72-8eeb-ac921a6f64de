# PzSlot 二开插槽组件使用示例

## 组件说明

PzSlot 是一个高级组件，用于在表单中提供插槽功能，允许前端在引入表单时对指定的插槽注入自定义的业务组件。

## 主要特性

1. **插槽名称属性**：通过 `slotName` 属性定义插槽的唯一标识符
2. **插槽数据传递**：通过 `slotData` 属性向插槽传递数据
3. **表单数据注入**：自动注入表单数据和参数供插槽使用
4. **占位符显示**：当插槽未定义或未配置时显示友好的占位符

## 配置方式

在表单设计器中：

1. 从高级控件面板拖拽"二开插槽"组件到表单中
2. 在右侧属性面板配置：
   - **插槽名称**：输入插槽的唯一标识符（如：`customBusinessComponent`）
   - **插槽数据**：输入要传递给插槽的数据（JSON格式）

## 使用示例

### 1. 在表单设计器中配置

```json
{
  "slotName": "customBusinessComponent",
  "slotData": {
    "title": "自定义业务组件",
    "config": {
      "showHeader": true,
      "allowEdit": false
    }
  }
}
```

### 2. 在前端页面中使用

```vue
<template>
  <div>
    <!-- 引入包含 PzSlot 的表单 -->
    <DynamicForm :config="formConfig">
      <!-- 通过具名插槽注入自定义组件 -->
      <template #customBusinessComponent="{ slotData }">
        <CustomBusinessComponent 
          :title="slotData.title"
          :config="slotData.config"
          :formData="slotData.formData"
          :parameter="slotData.parameter"
        />
      </template>
    </DynamicForm>
  </div>
</template>

<script setup>
import DynamicForm from '@/components/DynamicForm'
import CustomBusinessComponent from './CustomBusinessComponent.vue'

const formConfig = {
  // 表单配置...
}
</script>
```

### 3. 自定义业务组件示例

```vue
<template>
  <div class="custom-business-component">
    <h3 v-if="config.showHeader">{{ title }}</h3>
    <div class="content">
      <p>表单数据：{{ JSON.stringify(formData) }}</p>
      <p>参数：{{ JSON.stringify(parameter) }}</p>
      <!-- 自定义业务逻辑 -->
    </div>
  </div>
</template>

<script setup>
defineProps({
  title: String,
  config: Object,
  formData: Object,
  parameter: Object
})
</script>
```

## 数据传递

PzSlot 组件会自动向插槽传递以下数据：

- `slotData.formData`：当前表单的所有数据
- `slotData.parameter`：表单的参数对象
- `slotData.*`：在配置中定义的自定义数据

## 注意事项

1. 插槽名称必须唯一，避免冲突
2. 插槽数据必须是有效的 JSON 格式
3. 如果插槽未定义，组件会显示友好的占位符提示
4. 插槽组件可以访问完整的表单上下文数据

## 应用场景

- 嵌入第三方组件
- 自定义业务逻辑组件
- 动态图表展示
- 复杂的数据处理组件
- 与外部系统的集成组件

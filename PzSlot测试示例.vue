<template>
  <div class="pz-slot-test">
    <h2>PzSlot 组件测试示例</h2>
    
    <!-- 使用 Parser 组件渲染包含 PzSlot 的表单 -->
    <Parser :formConf="formConfig">
      <!-- 自定义插槽：用户信息展示 -->
      <template #userInfoDisplay="{ slotData }">
        <div class="user-info-card">
          <h4>用户信息展示</h4>
          <p><strong>当前用户：</strong>{{ slotData.formData.userName || '未填写' }}</p>
          <p><strong>邮箱：</strong>{{ slotData.formData.email || '未填写' }}</p>
          <p><strong>表单ID：</strong>{{ slotData.parameter.formId || '无' }}</p>
          <div class="custom-data">
            <h5>自定义数据：</h5>
            <pre>{{ JSON.stringify(slotData.customConfig, null, 2) }}</pre>
          </div>
        </div>
      </template>
      
      <!-- 自定义插槽：操作按钮组 -->
      <template #actionButtons="{ slotData }">
        <div class="action-buttons">
          <a-button type="primary" @click="handleCustomAction('save', slotData)">
            保存数据
          </a-button>
          <a-button @click="handleCustomAction('preview', slotData)">
            预览
          </a-button>
          <a-button danger @click="handleCustomAction('reset', slotData)">
            重置表单
          </a-button>
        </div>
      </template>
      
      <!-- 自定义插槽：数据统计 -->
      <template #dataStats="{ slotData }">
        <div class="data-stats">
          <a-row :gutter="16">
            <a-col :span="8">
              <a-statistic title="表单字段数" :value="Object.keys(slotData.formData).length" />
            </a-col>
            <a-col :span="8">
              <a-statistic title="已填写字段" :value="getFilledFieldsCount(slotData.formData)" />
            </a-col>
            <a-col :span="8">
              <a-statistic title="完成度" :value="getCompletionRate(slotData.formData)" suffix="%" />
            </a-col>
          </a-row>
        </div>
      </template>
    </Parser>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import Parser from '@/components/FormGenerator/src/components/Parser.vue'

// 模拟表单配置，包含 PzSlot 组件
const formConfig = ref({
  formRef: 'elForm',
  formModel: 'formData',
  size: 'default',
  labelPosition: 'right',
  labelWidth: 100,
  formRules: 'rules',
  gutter: 15,
  disabled: false,
  span: 24,
  formBtns: true,
  fields: [
    // 普通输入框
    {
      __config__: {
        jnpfKey: 'input',
        label: '用户名',
        showLabel: true,
        tag: 'JnpfInput',
        tagIcon: 'icon-ym icon-ym-generator-input',
        defaultValue: '',
        required: true,
        layout: 'colFormItem',
        span: 24,
        renderKey: 'input_' + Date.now(),
        visibility: ['pc', 'app'],
        noShow: false,
      },
      __vModel__: 'userName',
      placeholder: '请输入用户名',
    },
    // 邮箱输入框
    {
      __config__: {
        jnpfKey: 'input',
        label: '邮箱',
        showLabel: true,
        tag: 'JnpfInput',
        tagIcon: 'icon-ym icon-ym-generator-input',
        defaultValue: '',
        required: false,
        layout: 'colFormItem',
        span: 24,
        renderKey: 'email_' + Date.now(),
        visibility: ['pc', 'app'],
        noShow: false,
      },
      __vModel__: 'email',
      placeholder: '请输入邮箱地址',
    },
    // PzSlot 组件 - 用户信息展示
    {
      __config__: {
        jnpfKey: 'pzSlot',
        label: '用户信息展示',
        showLabel: true,
        tag: 'JnpfPzSlot',
        tagIcon: 'icon-ym icon-ym-generator-slot',
        defaultValue: null,
        required: false,
        layout: 'colFormItem',
        span: 24,
        renderKey: 'pzSlot1_' + Date.now(),
        visibility: ['pc', 'app'],
        noShow: false,
      },
      slotName: 'userInfoDisplay',
      slotData: {
        customConfig: {
          showAvatar: true,
          theme: 'light'
        }
      },
    },
    // PzSlot 组件 - 操作按钮
    {
      __config__: {
        jnpfKey: 'pzSlot',
        label: '操作按钮',
        showLabel: true,
        tag: 'JnpfPzSlot',
        tagIcon: 'icon-ym icon-ym-generator-slot',
        defaultValue: null,
        required: false,
        layout: 'colFormItem',
        span: 24,
        renderKey: 'pzSlot2_' + Date.now(),
        visibility: ['pc', 'app'],
        noShow: false,
      },
      slotName: 'actionButtons',
      slotData: {
        buttonSize: 'default',
        showIcons: true
      },
    },
    // PzSlot 组件 - 数据统计
    {
      __config__: {
        jnpfKey: 'pzSlot',
        label: '数据统计',
        showLabel: true,
        tag: 'JnpfPzSlot',
        tagIcon: 'icon-ym icon-ym-generator-slot',
        defaultValue: null,
        required: false,
        layout: 'colFormItem',
        span: 24,
        renderKey: 'pzSlot3_' + Date.now(),
        visibility: ['pc', 'app'],
        noShow: false,
      },
      slotName: 'dataStats',
      slotData: {
        refreshInterval: 5000
      },
    },
  ]
})

// 处理自定义操作
function handleCustomAction(action: string, slotData: any) {
  console.log('执行自定义操作:', action, slotData)
  
  switch (action) {
    case 'save':
      console.log('保存表单数据:', slotData.formData)
      break
    case 'preview':
      console.log('预览表单数据:', slotData.formData)
      break
    case 'reset':
      console.log('重置表单')
      break
  }
}

// 计算已填写字段数量
function getFilledFieldsCount(formData: any) {
  return Object.values(formData).filter(value => 
    value !== null && value !== undefined && value !== ''
  ).length
}

// 计算完成度
function getCompletionRate(formData: any) {
  const totalFields = Object.keys(formData).length
  const filledFields = getFilledFieldsCount(formData)
  return totalFields > 0 ? Math.round((filledFields / totalFields) * 100) : 0
}
</script>

<style scoped>
.pz-slot-test {
  padding: 20px;
  max-width: 800px;
  margin: 0 auto;
}

.user-info-card {
  padding: 16px;
  border: 1px solid #d9d9d9;
  border-radius: 6px;
  background-color: #fafafa;
}

.custom-data {
  margin-top: 12px;
}

.custom-data pre {
  background-color: #f5f5f5;
  padding: 8px;
  border-radius: 4px;
  font-size: 12px;
}

.action-buttons {
  display: flex;
  gap: 8px;
  flex-wrap: wrap;
}

.data-stats {
  padding: 16px;
  background-color: #f9f9f9;
  border-radius: 6px;
}
</style>

<template>
  <div ref="chartRef" style="width: 100%; height: 220px"></div>
</template>
<script lang="ts" setup>
import { useEChart } from "@/components/VisualPortal/Design/hooks/useEChart";
import { onMounted, ref, Ref } from "vue";
const activeData = {
  jnpfKey: "lineChart",
  dataType: "static",
  propsApi: "",
  option: {
    styleType: 2,
    defaultValue: [
      {
        name: "苹果",
        type: "手机品牌",
        value: 1000879,
      },
      {
        name: "三星",
        type: "手机品牌",
        value: 3400879,
      },
      {
        name: "小米",
        type: "手机品牌",
        value: 2300879,
      },
      {
        name: "oppo",
        type: "手机品牌",
        value: 5400879,
      },
      {
        name: "vivo",
        type: "手机品牌",
        value: 3400879,
      },
    ],
    areaStyle: true,
    titleText: "",
    titleTextStyleColor: "#303133",
    titleTextStyleFontSize: 18,
    titleTextStyleFontWeight: false,
    titleLeft: "center",
    titleBgColor: "",
    titleSubtext: "",
    titleSubtextStyleColor: "#303133",
    titleSubtextStyleFontSize: 12,
    titleSubtextStyleFontWeight: false,
    xAxisName: "",
    xAxisNameTextStyleFontSize: 14,
    xAxisNameTextStyleColor: "#303133",
    xAxisNameTextStyleFontWeight: false,
    xAxisShow: false,
    category: "category",
    xAxisAxisLineLineStyleColor: "#303133",
    xAxisAxisLabelRotate: 0,
    xAxisAxisLabelTextStyleFontSize: 14,
    xAxisAxisLabelTextStyleColor: "#303133",
    xAxisAxisLabelTextFontWeight: false,
    xAxisSplitLineShow: false,
    xAxisSplitLineLineStyleColor: "#DFDFDF",
    xAxisInverse: false,
    yAxisName: "",
    yAxisNameTextStyleFontSize: 14,
    yAxisNameTextStyleColor: "#303133",
    yAxisNameTextStyleFontWeight: false,
    yAxisShow: false,
    yAxisAxisLineLineStyleColor: "#303133",
    yAxisSplitLineShow: true,
    yAxisSplitLineLineStyleColor: "#DFDFDF",
    yAxisAxisLabelTextStyleFontSize: 14,
    yAxisAxisLabelTextStyleColor: "#303133",
    yAxisAxisLabelTextFontWeight: false,
    seriesLabelShow: false,
    seriesLabelFontSize: 14,
    seriesLabelFontWeight: false,
    seriesLabelColor: "#303133",
    seriesLabelBgColor: "",
    seriesLineStyleWidth: 2,
    seriesSymbolRotate: 0,
    tooltipShow: false,
    tooltipTextStyleFontSize: 14,
    tooltipTextStyleFontWeight: false,
    tooltipTextStyleColor: "#303133",
    tooltipBgColor: "#fff",
    gridLeft: -50,
    gridTop: 31,
    gridRight: 20,
    gridBottom: 20,
    legendShow: false,
    legendTextStyleFontSize: 14,
    legendOrient: "horizontal",
    legendLeft: 0,
    legendTop: 0,
    AxisTextStyleColor: "",
    AxisLineStyleColor: null,
    colorList: [
      {
        color1: "#1a9dff",
        color2: "#FFFFFF",
      },
    ],
    target: "_self",
    urlAddress: "",
  },
  mappingConfig: [
    {
      field: "系列",
      value: "",
    },
    {
      field: "维度",
      value: "",
    },
    {
      field: "数值",
      value: "",
    },
  ],
  refresh: {
    autoRefresh: false,
    autoRefreshTime: 5,
  },
};
const chartRef = ref<HTMLDivElement | null>(null);
const { CardHeader, init } = useEChart(activeData, chartRef as Ref<HTMLDivElement>);

onMounted(() => init());
</script>

<template>
  <div ref="chartRef" style="width: 100%; height: 260px"></div>
</template>
<script lang="ts" setup>
import { useEChart } from "@/components/VisualPortal/Design/hooks/useEChart";
import { onMounted, ref, Ref } from "vue";
const activeData = {
  jnpfKey: "lineChart",
  dataType: "static",
  option: {
    styleType: 1,
    defaultValue: [
      {
        name: "张三",
        type: "手机品牌",
        value: 1000879,
      },
      {
        name: "李四",
        type: "手机品牌",
        value: 3400879,
      },
      {
        name: "网二",
        type: "手机品牌",
        value: 2300879,
      },
      {
        name: "交底书",
        type: "手机品牌",
        value: 5400879,
      },
      {
        name: "弄清",
        type: "手机品牌",
        value: 3400879,
      },
    ],
    areaStyle: false,
    titleText: "",
    titleTextStyleColor: "#303133",
    titleTextStyleFontSize: 18,
    titleTextStyleFontWeight: false,
    titleLeft: "center",
    titleBgColor: "",
    titleSubtext: "",
    titleSubtextStyleColor: "#303133",
    titleSubtextStyleFontSize: 12,
    titleSubtextStyleFontWeight: false,
    xAxisName: "",
    xAxisNameTextStyleFontSize: 14,
    xAxisNameTextStyleColor: "#303133",
    xAxisNameTextStyleFontWeight: false,
    xAxisShow: true,
    category: "category",
    xAxisAxisLineLineStyleColor: "#303133",
    xAxisAxisLabelRotate: 0,
    xAxisAxisLabelTextStyleFontSize: 14,
    xAxisAxisLabelTextStyleColor: "#303133",
    xAxisAxisLabelTextFontWeight: false,
    xAxisSplitLineShow: false,
    xAxisSplitLineLineStyleColor: "#DFDFDF",
    xAxisInverse: false,
    yAxisName: "",
    yAxisNameTextStyleFontSize: 14,
    yAxisNameTextStyleColor: "#303133",
    yAxisNameTextStyleFontWeight: false,
    yAxisShow: false,
    yAxisAxisLineLineStyleColor: "#303133",
    yAxisSplitLineShow: true,
    yAxisSplitLineLineStyleColor: "#DFDFDF",
    yAxisAxisLabelTextStyleFontSize: 14,
    yAxisAxisLabelTextStyleColor: "#303133",
    yAxisAxisLabelTextFontWeight: false,
    seriesLabelShow: true,
    seriesLabelFontSize: 14,
    seriesLabelFontWeight: false,
    seriesLabelColor: "#303133",
    seriesLabelBgColor: "",
    seriesLineStyleWidth: 2,
    seriesSymbolRotate: 4,
    tooltipShow: true,
    tooltipTextStyleFontSize: 14,
    tooltipTextStyleFontWeight: false,
    tooltipTextStyleColor: "#303133",
    tooltipBgColor: "#fff",
    gridLeft: -20,
    gridTop: 50,
    gridRight: 20,
    gridBottom: 10,
    legendShow: false,
    legendTextStyleFontSize: 14,
    legendOrient: "horizontal",
    legendLeft: 0,
    legendTop: 0,
    AxisTextStyleColor: "",
    AxisLineStyleColor: "",
    colorList: [],
    target: "_self",
    urlAddress: "",
    yAxisInverse: false,
  },

};
const chartRef = ref<HTMLDivElement | null>(null);
const { CardHeader, init } = useEChart(activeData, chartRef as Ref<HTMLDivElement>);

onMounted(() => init());
</script>

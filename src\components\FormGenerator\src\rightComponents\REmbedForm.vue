<template>
  <a-form-item label="菜单ID">
    <a-input
      v-model:value="activeData.menuId"
      placeholder="请输入菜单ID"
      :disabled="activeData.disabled" />
  </a-form-item>
  <a-form-item label="表单ID">
    <a-input
      v-model:value="activeData.formId"
      placeholder="请输入表单ID"
      :disabled="activeData.disabled" />
  </a-form-item>
  <a-form-item label="是否流程">
    <a-switch
      v-model:checked="activeData.isFlow"
      :disabled="activeData.disabled" />
  </a-form-item>
</template>
<script lang="ts" setup>
  import { ref } from 'vue';
  defineOptions({ inheritAttrs: false });
  const props = defineProps(['activeData']);
  const renderKey = ref(+new Date());
</script>
